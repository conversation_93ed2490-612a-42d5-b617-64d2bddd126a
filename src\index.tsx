import { Hono } from "hono";
import { cors } from "hono/cors";
import { SeriesController } from "./controllers/series";
import { SeriesStatus } from "./types/series";

type CloudflareBindings = {
  DATABASE_URL: string;
};

const app = new Hono<{ Bindings: CloudflareBindings }>();

// Add CORS middleware
app.use(
  "*",
  cors({
    origin: "http://localhost:5173",
    allowMethods: ["GET", "POST", "PUT", "DELETE", "OPTIONS"],
    allowHeaders: ["Content-Type", "Authorization"],
  })
);

// Initialize controller
const seriesController = new SeriesController();

// Health check endpoint
app.get("/", (c) => {
  return c.json({
    message: "Series API is running",
    version: "1.0.0",
    endpoints: {
      "GET /api/series": "List all series with pagination",
      "GET /api/series/:id": "Get series by ID",
      "POST /api/series": "Create new series",
      "PUT /api/series/:id": "Update series",
      "DELETE /api/series/:id": "Delete series",
      "GET /api/statuses": "Get available series statuses",
    },
  });
});

// API Routes
app.get("/api/series", (c) => seriesController.getAllSeries(c));
app.get("/api/series/:id", (c) => seriesController.getSeriesById(c));
app.post("/api/series", (c) => seriesController.createSeries(c));
app.put("/api/series/:id", (c) => seriesController.updateSeries(c));
app.delete("/api/series/:id", (c) => seriesController.deleteSeries(c));

// Statuses endpoint
app.get("/api/statuses", (c) => {
  return c.json({
    success: true,
    data: Object.values(SeriesStatus),
  });
});

// 404 handler
app.notFound((c) => {
  return c.json(
    {
      success: false,
      error: "Endpoint not found",
      message: "The requested endpoint does not exist",
    },
    404
  );
});

// Error handler
app.onError((err, c) => {
  console.error("Unhandled error:", err);
  return c.json(
    {
      success: false,
      error: "Internal server error",
      message: "An unexpected error occurred",
    },
    500
  );
});

export default app;
