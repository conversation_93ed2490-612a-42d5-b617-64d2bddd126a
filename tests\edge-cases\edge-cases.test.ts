import { 
  CreateSeriesSchema, 
  UpdateSeriesSchema, 
  PaginationSchema,
  SeriesStatus 
} from '../../src/types/series';
import { 
  testScenarios,
  expectValidationError,
  expectValidationSuccess 
} from '../utils/testHelpers';

describe('Edge Cases and Error Scenarios', () => {
  describe('Schema Validation Edge Cases', () => {
    describe('CreateSeriesSchema', () => {
      it('should handle very long series names', () => {
        const longName = 'a'.repeat(256); // Over 255 character limit
        const result = CreateSeriesSchema.safeParse({ name: longName });
        expectValidationError(result, 'name');
      });

      it('should handle maximum valid name length', () => {
        const maxName = 'a'.repeat(255); // Exactly 255 characters
        const result = CreateSeriesSchema.safeParse({ name: maxName });
        expectValidationSuccess(result);
      });

      it('should handle zero chapter number', () => {
        const result = CreateSeriesSchema.safeParse({ 
          name: 'Test', 
          chapter: 0 
        });
        expectValidationError(result, 'chapter');
      });

      it('should handle very large chapter numbers', () => {
        const result = CreateSeriesSchema.safeParse({ 
          name: 'Test', 
          chapter: Number.MAX_SAFE_INTEGER 
        });
        expectValidationSuccess(result);
      });

      it('should handle decimal chapter numbers', () => {
        const result = CreateSeriesSchema.safeParse({ 
          name: 'Test', 
          chapter: 123.5 
        });
        expectValidationSuccess(result);
      });

      it('should handle null and undefined values correctly', () => {
        const resultWithNull = CreateSeriesSchema.safeParse({ 
          name: 'Test', 
          chapter: null,
          status: null 
        });
        expectValidationSuccess(resultWithNull);

        const resultWithUndefined = CreateSeriesSchema.safeParse({ 
          name: 'Test', 
          chapter: undefined,
          status: undefined 
        });
        expectValidationSuccess(resultWithUndefined);
      });

      it('should handle whitespace-only names', () => {
        const result = CreateSeriesSchema.safeParse({ name: '   ' });
        expectValidationError(result, 'name');
      });

      it('should handle special characters in names', () => {
        const specialChars = 'Test Series: Part 1 - "The Beginning" (2024) [Updated]';
        const result = CreateSeriesSchema.safeParse({ name: specialChars });
        expectValidationSuccess(result);
      });

      it('should handle unicode characters in names', () => {
        const unicodeName = 'テストシリーズ 测试系列 тестовая серия';
        const result = CreateSeriesSchema.safeParse({ name: unicodeName });
        expectValidationSuccess(result);
      });
    });

    describe('PaginationSchema', () => {
      it('should handle string numbers correctly', () => {
        const result = PaginationSchema.safeParse({
          page: '5',
          pageSize: '25'
        });
        expectValidationSuccess(result);
        expect(result.success && result.data.page).toBe(5);
        expect(result.success && result.data.pageSize).toBe(25);
      });

      it('should handle non-numeric strings', () => {
        const result = PaginationSchema.safeParse({
          page: 'abc',
          limit: 'xyz'
        });
        expectValidationError(result);
      });

      it('should handle floating point strings', () => {
        const result = PaginationSchema.safeParse({
          page: '1.5',
          limit: '10.7'
        });
        // Should parse as integers
        expectValidationSuccess(result);
        expect(result.success && result.data.page).toBe(1);
        expect(result.success && result.data.pageSize).toBe(10);
      });

      it('should handle boundary values', () => {
        const maxLimitResult = PaginationSchema.safeParse({ limit: '100' });
        expectValidationSuccess(maxLimitResult);

        const overLimitResult = PaginationSchema.safeParse({ limit: '101' });
        expectValidationError(overLimitResult);

        const minPageResult = PaginationSchema.safeParse({ page: '1' });
        expectValidationSuccess(minPageResult);

        const underPageResult = PaginationSchema.safeParse({ page: '0' });
        expectValidationError(underPageResult);
      });

      it('should handle empty strings', () => {
        const result = PaginationSchema.safeParse({
          page: '',
          limit: ''
        });
        expectValidationError(result);
      });

      it('should handle case sensitivity in enum values', () => {
        const lowerCaseResult = PaginationSchema.safeParse({ 
          sort: 'name',
          order: 'asc',
          status: 'reading' // lowercase
        });
        expectValidationError(lowerCaseResult, 'status');

        const correctCaseResult = PaginationSchema.safeParse({ 
          sort: 'name',
          order: 'asc',
          status: 'Reading' // correct case
        });
        expectValidationSuccess(correctCaseResult);
      });
    });
  });

  describe('Data Type Edge Cases', () => {
    it('should handle all valid status values', () => {
      const statusValues = Object.values(SeriesStatus);
      
      statusValues.forEach(status => {
        const result = CreateSeriesSchema.safeParse({
          name: 'Test Series',
          status: status
        });
        expectValidationSuccess(result);
      });
    });

    it('should reject invalid status values', () => {
      const invalidStatuses = [
        'reading', // lowercase
        'READING', // uppercase
        'In Progress', // different wording
        'Finished', // different wording
        'Paused', // different wording
        '', // empty
        null, // null is actually valid
        undefined, // undefined is actually valid
      ];

      invalidStatuses.forEach(status => {
        if (status !== null && status !== undefined) {
          const result = CreateSeriesSchema.safeParse({
            name: 'Test Series',
            status: status
          });
          expectValidationError(result, 'status');
        }
      });
    });

    it('should handle extreme date values', () => {
      const veryOldDate = new Date('1900-01-01');
      const veryNewDate = new Date('2100-12-31');
      const invalidDate = new Date('invalid');

      // These would be handled at the database/application level
      // The schema itself doesn't validate date ranges
      expect(veryOldDate instanceof Date).toBe(true);
      expect(veryNewDate instanceof Date).toBe(true);
      expect(isNaN(invalidDate.getTime())).toBe(true);
    });
  });

  describe('Boundary Conditions', () => {
    it('should handle minimum valid inputs', () => {
      const minimalCreate = CreateSeriesSchema.safeParse({
        name: 'A' // Single character name
      });
      expectValidationSuccess(minimalCreate);

      const minimalUpdate = UpdateSeriesSchema.safeParse({});
      expectValidationSuccess(minimalUpdate);

      const minimalPagination = PaginationSchema.safeParse({});
      expectValidationSuccess(minimalPagination);
    });

    it('should handle maximum valid inputs', () => {
      const maximalCreate = CreateSeriesSchema.safeParse({
        name: 'a'.repeat(255),
        chapter: Number.MAX_SAFE_INTEGER,
        status: SeriesStatus.PLAN_TO_READ
      });
      expectValidationSuccess(maximalCreate);

      const maximalPagination = PaginationSchema.safeParse({
        page: '999999',
        pageSize: '100',
        sortBy: 'updatedAt',
        sortOrder: 'desc',
        status: SeriesStatus.PLAN_TO_READ,
        search: 'a'.repeat(1000) // Very long search term
      });
      expectValidationSuccess(maximalPagination);
    });
  });

  describe('Type Coercion Edge Cases', () => {
    it('should handle numeric strings in pagination', () => {
      const result = PaginationSchema.safeParse({
        page: '  5  ', // with whitespace
        limit: '20'
      });
      
      // Note: Zod's string().transform() might not trim whitespace
      // This test verifies the actual behavior
      if (result.success) {
        expect(typeof result.data.page).toBe('number');
        expect(typeof result.data.pageSize).toBe('number');
      }
    });

    it('should handle boolean-like strings', () => {
      const result = PaginationSchema.safeParse({
        page: 'true', // Not a valid number
        pageSize: 'false' // Not a valid number
      });
      expectValidationError(result);
    });

    it('should handle scientific notation', () => {
      const result = PaginationSchema.safeParse({
        page: '1e1', // 10 in scientific notation
        pageSize: '2e1'  // 20 in scientific notation
      });

      if (result.success) {
        expect(result.data.page).toBe(10);
        expect(result.data.pageSize).toBe(20);
      }
    });
  });

  describe('Concurrent Access Scenarios', () => {
    it('should handle multiple validation calls', async () => {
      const promises = Array.from({ length: 100 }, (_, i) => 
        Promise.resolve(CreateSeriesSchema.safeParse({
          name: `Test Series ${i}`,
          chapter: i,
          status: i % 2 === 0 ? SeriesStatus.READING : SeriesStatus.COMPLETED
        }))
      );

      const results = await Promise.all(promises);
      
      results.forEach((result, index) => {
        expectValidationSuccess(result);
        expect(result.success && result.data.name).toBe(`Test Series ${index}`);
      });
    });
  });

  describe('Memory and Performance Edge Cases', () => {
    it('should handle large arrays of validation', () => {
      const largeArray = Array.from({ length: 1000 }, (_, i) => ({
        name: `Series ${i}`,
        chapter: i,
        status: SeriesStatus.READING
      }));

      const startTime = Date.now();
      const results = largeArray.map(item => CreateSeriesSchema.safeParse(item));
      const endTime = Date.now();

      // Should complete within reasonable time (less than 1 second)
      expect(endTime - startTime).toBeLessThan(1000);
      
      // All should be valid
      results.forEach(result => {
        expectValidationSuccess(result);
      });
    });

    it('should handle deeply nested objects gracefully', () => {
      const deepObject = {
        name: 'Test',
        nested: {
          level1: {
            level2: {
              level3: 'deep value'
            }
          }
        }
      };

      // Schema should ignore unknown properties
      const result = CreateSeriesSchema.safeParse(deepObject);
      expectValidationSuccess(result);
      expect(result.success && result.data.name).toBe('Test');
      expect(result.success && (result.data as any).nested).toBeUndefined();
    });
  });
});
