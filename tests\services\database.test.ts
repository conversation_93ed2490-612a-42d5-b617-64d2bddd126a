import { DatabaseService } from '../../src/services/database';
import { SeriesStatus } from '../../src/types/series';

// Mock the neon database client
jest.mock('@neondatabase/serverless', () => ({
  neon: jest.fn(() => jest.fn()),
}));

describe('DatabaseService', () => {
  let databaseService: DatabaseService;
  let mockSql: jest.MockedFunction<any>;

  beforeEach(() => {
    // Reset mocks
    jest.clearAllMocks();
    
    // Create mock SQL function
    mockSql = jest.fn();
    mockSql.unsafe = jest.fn().mockImplementation((query: string) => query);

    // Mock the neon constructor to return our mock SQL function
    const { neon } = require('@neondatabase/serverless');
    (neon as jest.Mock).mockReturnValue(mockSql);
    
    databaseService = new DatabaseService('test://connection');
  });

  describe('getAllSeries', () => {
    it('should return paginated series without filters', async () => {
      const mockCountResult = [{ count: '25' }];
      const mockDataResult = [
        {
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Test Series',
          chapter: 100,
          status: SeriesStatus.READING,
          updated_at: '2024-01-01T00:00:00.000Z',
        },
      ];

      // Mock the SQL calls
      mockSql
        .mockResolvedValueOnce(mockCountResult) // Count query
        .mockResolvedValueOnce(mockDataResult); // Data query

      const query = {
        page: 1,
        pageSize: 10,
        sortBy: 'updatedAt' as const,
        sortOrder: 'desc' as const,
      };

      const result = await databaseService.getAllSeries(query);

      expect(result.data).toHaveLength(1);
      expect(result.data[0].name).toBe('Test Series');
      expect(result.totalCount).toBe(25);
      expect(result.page).toBe(1);
      expect(result.pageSize).toBe(10);
      expect(result.totalPages).toBe(3);
      expect(result.hasNextPage).toBe(true);
      expect(result.hasPreviousPage).toBe(false);
    });

    it('should return filtered series by status', async () => {
      const mockCountResult = [{ count: '5' }];
      const mockDataResult = [
        {
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Reading Series',
          chapter: 50,
          status: SeriesStatus.READING,
          updated_at: '2024-01-01T00:00:00.000Z',
        },
      ];

      mockSql
        .mockResolvedValueOnce(mockCountResult)
        .mockResolvedValueOnce(mockDataResult);

      const query = {
        page: 1,
        pageSize: 10,
        sortBy: 'updatedAt' as const,
        sortOrder: 'desc' as const,
        status: SeriesStatus.READING,
      };

      const result = await databaseService.getAllSeries(query);

      expect(result.data).toHaveLength(1);
      expect(result.data[0].status).toBe(SeriesStatus.READING);
      expect(result.totalCount).toBe(5);
    });

    it('should return searched series', async () => {
      const mockCountResult = [{ count: '1' }];
      const mockDataResult = [
        {
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Naruto',
          chapter: 700,
          status: SeriesStatus.COMPLETED,
          updated_at: '2024-01-01T00:00:00.000Z',
        },
      ];

      mockSql
        .mockResolvedValueOnce(mockCountResult)
        .mockResolvedValueOnce(mockDataResult);

      const query = {
        page: 1,
        pageSize: 10,
        sortBy: 'updatedAt' as const,
        sortOrder: 'desc' as const,
        search: 'naruto',
      };

      const result = await databaseService.getAllSeries(query);

      expect(result.data).toHaveLength(1);
      expect(result.data[0].name).toBe('Naruto');
      expect(result.totalCount).toBe(1);
    });
  });

  describe('getSeriesById', () => {
    it('should return series when found', async () => {
      const mockResult = [
        {
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Test Series',
          chapter: 100,
          status: SeriesStatus.READING,
          updated_at: '2024-01-01T00:00:00.000Z',
        },
      ];

      mockSql.mockResolvedValue(mockResult);

      const result = await databaseService.getSeriesById('123e4567-e89b-12d3-a456-426614174000');

      expect(result).not.toBeNull();
      expect(result?.name).toBe('Test Series');
      expect(result?.id).toBe('123e4567-e89b-12d3-a456-426614174000');
    });

    it('should return null when series not found', async () => {
      mockSql.mockResolvedValue([]);

      const result = await databaseService.getSeriesById('non-existent-id');

      expect(result).toBeNull();
    });
  });

  describe('createSeries', () => {
    it('should create and return new series', async () => {
      const mockResult = [
        {
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'New Series',
          chapter: 1,
          status: SeriesStatus.READING,
          updated_at: '2024-01-01T00:00:00.000Z',
        },
      ];

      mockSql.mockResolvedValue(mockResult);

      const input = {
        name: 'New Series',
        chapter: 1,
        status: SeriesStatus.READING,
      };

      const result = await databaseService.createSeries(input);

      expect(result.name).toBe('New Series');
      expect(result.chapter).toBe(1);
      expect(result.status).toBe(SeriesStatus.READING);
      expect(result.id).toBe('123e4567-e89b-12d3-a456-426614174000');
    });
  });

  describe('updateSeries', () => {
    it('should update and return series', async () => {
      const mockResult = [
        {
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Updated Series',
          chapter: 150,
          status: SeriesStatus.COMPLETED,
          updated_at: '2024-01-01T00:00:00.000Z',
        },
      ];

      mockSql.mockResolvedValue(mockResult);

      const input = {
        name: 'Updated Series',
        chapter: 150,
        status: SeriesStatus.COMPLETED,
      };

      const result = await databaseService.updateSeries('123e4567-e89b-12d3-a456-426614174000', input);

      expect(result).not.toBeNull();
      expect(result?.name).toBe('Updated Series');
      expect(result?.chapter).toBe(150);
      expect(result?.status).toBe(SeriesStatus.COMPLETED);
    });

    it('should return existing series when no updates provided', async () => {
      const mockGetResult = [
        {
          id: '123e4567-e89b-12d3-a456-426614174000',
          name: 'Existing Series',
          chapter: 100,
          status: SeriesStatus.READING,
          updated_at: '2024-01-01T00:00:00.000Z',
        },
      ];

      mockSql.mockResolvedValue(mockGetResult);

      const result = await databaseService.updateSeries('123e4567-e89b-12d3-a456-426614174000', {});

      expect(result).not.toBeNull();
      expect(result?.name).toBe('Existing Series');
    });

    it('should return null when series not found', async () => {
      mockSql.mockResolvedValue([]);

      const input = {
        name: 'Updated Series',
      };

      const result = await databaseService.updateSeries('non-existent-id', input);

      expect(result).toBeNull();
    });
  });

  describe('deleteSeries', () => {
    it('should return true when series is successfully deleted', async () => {
      // Mock: series exists, then gets deleted
      mockSql
        .mockResolvedValueOnce([{ id: '123' }]) // exists check
        .mockResolvedValueOnce([]) // delete operation
        .mockResolvedValueOnce([]); // verify deletion

      const result = await databaseService.deleteSeries('123e4567-e89b-12d3-a456-426614174000');

      expect(result).toBe(true);
    });

    it('should return false when series does not exist', async () => {
      mockSql.mockResolvedValue([]); // series doesn't exist

      const result = await databaseService.deleteSeries('non-existent-id');

      expect(result).toBe(false);
    });
  });

  describe('seriesExists', () => {
    it('should return true when series exists', async () => {
      mockSql.mockResolvedValue([{ id: '123' }]);

      const result = await databaseService.seriesExists('Test Series');

      expect(result).toBe(true);
    });

    it('should return false when series does not exist', async () => {
      mockSql.mockResolvedValue([]);

      const result = await databaseService.seriesExists('Non-existent Series');

      expect(result).toBe(false);
    });

    it('should exclude specific ID when checking existence', async () => {
      mockSql.mockResolvedValue([]);

      const result = await databaseService.seriesExists('Test Series', 'exclude-this-id');

      expect(result).toBe(false);
    });
  });
});
