{"name": "novel-archives-series-api", "type": "module", "scripts": {"start": "vite", "build": "vite build", "preview": "$npm_execpath run build && vite preview", "deploy": "$npm_execpath run build && wrangler deploy", "cf-typegen": "wrangler types --env-interface CloudflareBindings", "test": "jest", "test:watch": "jest --watch", "test:coverage": "jest --coverage", "test:unit": "jest tests/types tests/services tests/controllers", "test:integration": "jest tests/integration", "test:ci": "jest --coverage --watchAll=false"}, "dependencies": {"@neondatabase/serverless": "^1.0.1", "hono": "^4.7.11"}, "devDependencies": {"@cloudflare/vite-plugin": "^1.2.3", "@types/jest": "^29.5.14", "@types/supertest": "^6.0.3", "jest": "^29.7.0", "supertest": "^7.1.1", "ts-jest": "^29.3.4", "vite": "^6.3.5", "vite-ssr-components": "^0.2.0", "wrangler": "^4.17.0"}}