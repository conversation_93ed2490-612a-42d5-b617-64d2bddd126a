import { z } from "zod";

// Series status enum
export const SeriesStatus = {
  READING: "Reading",
  COMPLETED: "Completed",
  ON_HOLD: "On-Hold",
  DROPPED: "Dropped",
  CANCELLED: "Cancelled",
  PLAN_TO_READ: "Plan to Read",
} as const;

export type SeriesStatusType = (typeof SeriesStatus)[keyof typeof SeriesStatus];

// Database model interface
export interface Series {
  id: string;
  name: string;
  chapter: number | null;
  status: SeriesStatusType | null;
  updatedAt: string; // ISO date string for API responses
}

// Zod schema for series validation
export const SeriesSchema = z.object({
  id: z.string().uuid(),
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
  chapter: z.number().positive().optional().nullable(),
  status: z
    .enum([
      SeriesStatus.READING,
      SeriesStatus.COMPLETED,
      SeriesStatus.ON_HOLD,
      SeriesStatus.DROPPED,
      SeriesStatus.CANCELLED,
      SeriesStatus.PLAN_TO_READ,
    ])
    .optional()
    .nullable(),
  updated_at: z.date(),
});

// Schema for creating a new series (without id and updated_at)
export const CreateSeriesSchema = z.object({
  name: z.string().min(1, "Name is required").max(255, "Name too long"),
  chapter: z.number().positive().optional().nullable(),
  status: z
    .enum([
      SeriesStatus.READING,
      SeriesStatus.COMPLETED,
      SeriesStatus.ON_HOLD,
      SeriesStatus.DROPPED,
      SeriesStatus.CANCELLED,
      SeriesStatus.PLAN_TO_READ,
    ])
    .optional()
    .nullable(),
});

// Schema for updating a series (all fields optional except constraints)
export const UpdateSeriesSchema = z.object({
  name: z
    .string()
    .min(1, "Name is required")
    .max(255, "Name too long")
    .optional(),
  chapter: z.number().positive().optional().nullable(),
  status: z
    .enum([
      SeriesStatus.READING,
      SeriesStatus.COMPLETED,
      SeriesStatus.ON_HOLD,
      SeriesStatus.DROPPED,
      SeriesStatus.CANCELLED,
      SeriesStatus.PLAN_TO_READ,
    ])
    .optional()
    .nullable(),
});

// Pagination query parameters schema
export const PaginationSchema = z.object({
  page: z
    .string()
    .transform((val) => parseInt(val, 10))
    .pipe(z.number().min(1))
    .default("1"),
  pageSize: z
    .string()
    .transform((val) => parseInt(val, 10))
    .pipe(z.number().min(1).max(100))
    .default("10"),
  sortBy: z
    .enum(["name", "chapter", "status", "updatedAt"])
    .default("updatedAt"),
  sortOrder: z.enum(["asc", "desc"]).default("desc"),
  status: z
    .enum([
      SeriesStatus.READING,
      SeriesStatus.COMPLETED,
      SeriesStatus.ON_HOLD,
      SeriesStatus.DROPPED,
      SeriesStatus.CANCELLED,
      SeriesStatus.PLAN_TO_READ,
    ])
    .optional(),
  search: z.string().optional(),
});

// Types derived from schemas
export type CreateSeriesInput = z.infer<typeof CreateSeriesSchema>;
export type UpdateSeriesInput = z.infer<typeof UpdateSeriesSchema>;
export type PaginationQuery = z.infer<typeof PaginationSchema>;

// API response types
export interface PaginatedResponse<T> {
  data: T[];
  totalCount: number;
  page: number;
  pageSize: number;
  totalPages: number;
  hasNextPage: boolean;
  hasPreviousPage: boolean;
}

export interface ApiResponse<T> {
  success: boolean;
  data?: T;
  error?: string;
  message?: string;
}
