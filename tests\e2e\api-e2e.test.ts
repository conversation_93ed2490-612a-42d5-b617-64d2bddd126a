import app from '../../src/index';
import { SeriesStatus } from '../../src/types/series';

// Mock the database service for E2E tests
jest.mock('../../src/services/database');

describe('End-to-End API Tests', () => {
  let mockDatabaseService: any;
  let createdSeriesId: string;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockDatabaseService = {
      getAllSeries: jest.fn(),
      getSeriesById: jest.fn(),
      createSeries: jest.fn(),
      updateSeries: jest.fn(),
      deleteSeries: jest.fn(),
      seriesExists: jest.fn(),
    };

    const { DatabaseService } = require('../../src/services/database');
    DatabaseService.mockImplementation(() => mockDatabaseService);
  });

  describe('Complete CRUD Workflow', () => {
    it('should complete a full CRUD lifecycle', async () => {
      const testSeries = {
        name: 'E2E Test Series',
        chapter: 1,
        status: SeriesStatus.READING,
      };

      const createdSeries = {
        id: 'e2e-test-id-123',
        ...testSeries,
        updated_at: new Date('2024-01-01'),
      };

      const updatedSeries = {
        ...createdSeries,
        chapter: 50,
        status: SeriesStatus.COMPLETED,
        updated_at: new Date('2024-01-02'),
      };

      // Step 1: Create a new series
      mockDatabaseService.seriesExists.mockResolvedValue(false);
      mockDatabaseService.createSeries.mockResolvedValue(createdSeries);

      const createResponse = await app.request('/api/series', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testSeries),
      });

      expect(createResponse.status).toBe(201);
      const createBody = await createResponse.json();
      expect(createBody.success).toBe(true);
      expect(createBody.data.name).toBe(testSeries.name);
      createdSeriesId = createBody.data.id;

      // Step 2: Retrieve the created series
      mockDatabaseService.getSeriesById.mockResolvedValue(createdSeries);

      const getResponse = await app.request(`/api/series/${createdSeriesId}`);

      expect(getResponse.status).toBe(200);
      const getBody = await getResponse.json();
      expect(getBody.success).toBe(true);
      expect(getBody.data.id).toBe(createdSeriesId);

      // Step 3: Update the series
      mockDatabaseService.getSeriesById.mockResolvedValue(createdSeries);
      mockDatabaseService.seriesExists.mockResolvedValue(false);
      mockDatabaseService.updateSeries.mockResolvedValue(updatedSeries);

      const updateResponse = await app.request(`/api/series/${createdSeriesId}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({
          chapter: 50,
          status: SeriesStatus.COMPLETED,
        }),
      });

      expect(updateResponse.status).toBe(200);
      const updateBody = await updateResponse.json();
      expect(updateBody.success).toBe(true);
      expect(updateBody.data.chapter).toBe(50);
      expect(updateBody.data.status).toBe(SeriesStatus.COMPLETED);

      // Step 4: Verify the series appears in the list
      mockDatabaseService.getAllSeries.mockResolvedValue({
        data: [updatedSeries],
        pagination: {
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
      });

      const listResponse = await app.request('/api/series');

      expect(listResponse.status).toBe(200);
      const listBody = await listResponse.json();
      expect(listBody.success).toBe(true);
      expect(listBody.data.data).toHaveLength(1);
      expect(listBody.data.data[0].id).toBe(createdSeriesId);

      // Step 5: Delete the series
      mockDatabaseService.getSeriesById.mockResolvedValue(updatedSeries);
      mockDatabaseService.deleteSeries.mockResolvedValue(true);

      const deleteResponse = await app.request(`/api/series/${createdSeriesId}`, {
        method: 'DELETE',
      });

      expect(deleteResponse.status).toBe(200);
      const deleteBody = await deleteResponse.json();
      expect(deleteBody.success).toBe(true);
      expect(deleteBody.message).toBe('Series deleted successfully');

      // Step 6: Verify the series is gone
      mockDatabaseService.getSeriesById.mockResolvedValue(null);

      const verifyResponse = await app.request(`/api/series/${createdSeriesId}`);
      expect(verifyResponse.status).toBe(404);
    });
  });

  describe('Complex Query Scenarios', () => {
    it('should handle complex filtering and pagination', async () => {
      const mockSeries = Array.from({ length: 25 }, (_, i) => ({
        id: `series-${i}`,
        name: `Test Series ${i}`,
        chapter: i * 10,
        status: i % 3 === 0 ? SeriesStatus.READING : 
               i % 3 === 1 ? SeriesStatus.COMPLETED : SeriesStatus.ON_HOLD,
        updated_at: new Date(`2024-01-${String(i + 1).padStart(2, '0')}`),
      }));

      // Test pagination
      mockDatabaseService.getAllSeries.mockResolvedValue({
        data: mockSeries.slice(0, 10),
        pagination: {
          page: 1,
          limit: 10,
          total: 25,
          totalPages: 3,
          hasNext: true,
          hasPrev: false,
        },
      });

      const paginationResponse = await app.request('/api/series?page=1&limit=10');

      expect(paginationResponse.status).toBe(200);
      const paginationBody = await paginationResponse.json();
      expect(paginationBody.data.data).toHaveLength(10);
      expect(paginationBody.data.pagination.hasNext).toBe(true);

      // Test filtering by status
      const readingSeries = mockSeries.filter(s => s.status === SeriesStatus.READING);
      mockDatabaseService.getAllSeries.mockResolvedValue({
        data: readingSeries,
        pagination: {
          page: 1,
          limit: 10,
          total: readingSeries.length,
          totalPages: Math.ceil(readingSeries.length / 10),
          hasNext: false,
          hasPrev: false,
        },
      });

      const filterResponse = await app.request(`/api/series?status=${SeriesStatus.READING}`);

      expect(filterResponse.status).toBe(200);
      const filterBody = await filterResponse.json();
      expect(filterBody.data.data.every((s: any) => s.status === SeriesStatus.READING)).toBe(true);

      // Test search functionality
      const searchResults = mockSeries.filter(s => s.name.includes('5'));
      mockDatabaseService.getAllSeries.mockResolvedValue({
        data: searchResults,
        pagination: {
          page: 1,
          limit: 10,
          total: searchResults.length,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
      });

      const searchResponse = await app.request('/api/series?search=5');

      expect(searchResponse.status).toBe(200);
      const searchBody = await searchResponse.json();
      expect(searchBody.data.data.every((s: any) => s.name.includes('5'))).toBe(true);
    });
  });

  describe('Error Handling Scenarios', () => {
    it('should handle database connection errors gracefully', async () => {
      mockDatabaseService.getAllSeries.mockRejectedValue(new Error('Database connection failed'));

      const response = await app.request('/api/series');

      expect(response.status).toBe(500);
      const responseBody = await response.json();
      expect(responseBody.success).toBe(false);
      expect(responseBody.error).toBe('Internal server error');
    });

    it('should handle validation errors consistently', async () => {
      const invalidSeries = {
        name: '', // Invalid empty name
        chapter: -5, // Invalid negative chapter
        status: 'InvalidStatus', // Invalid status
      };

      const response = await app.request('/api/series', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(invalidSeries),
      });

      expect(response.status).toBe(400);
      const responseBody = await response.json();
      expect(responseBody.success).toBe(false);
      expect(responseBody.error).toBe('Invalid request data');
      expect(responseBody.details).toBeDefined();
    });

    it('should handle duplicate name conflicts', async () => {
      const duplicateSeries = {
        name: 'Existing Series',
        chapter: 1,
        status: SeriesStatus.READING,
      };

      mockDatabaseService.seriesExists.mockResolvedValue(true);

      const response = await app.request('/api/series', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(duplicateSeries),
      });

      expect(response.status).toBe(409);
      const responseBody = await response.json();
      expect(responseBody.success).toBe(false);
      expect(responseBody.error).toBe('Series with this name already exists');
    });
  });

  describe('Performance Under Load', () => {
    it('should handle multiple concurrent requests', async () => {
      mockDatabaseService.getAllSeries.mockResolvedValue({
        data: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
      });

      const concurrentRequests = Array.from({ length: 20 }, () =>
        app.request('/api/series')
      );

      const startTime = Date.now();
      const responses = await Promise.all(concurrentRequests);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second

      // Check all responses
      for (const response of responses) {
        expect(response.status).toBe(200);
        const body = await response.json();
        expect(body.success).toBe(true);
      }
    });

    it('should maintain response times under load', async () => {
      const largeSeries = {
        name: 'A'.repeat(255), // Maximum length name
        chapter: 999999,
        status: SeriesStatus.PLAN_TO_READ,
      };

      mockDatabaseService.seriesExists.mockResolvedValue(false);
      mockDatabaseService.createSeries.mockResolvedValue({
        id: 'large-series-id',
        ...largeSeries,
        updated_at: new Date(),
      });

      const startTime = Date.now();
      
      const response = await app.request('/api/series', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(largeSeries),
      });

      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(100); // Should respond quickly
      expect(response.status).toBe(201);
      const responseBody = await response.json();
      expect(responseBody.success).toBe(true);
    });
  });

  describe('Data Integrity Tests', () => {
    it('should maintain data consistency across operations', async () => {
      const testSeries = {
        name: 'Data Integrity Test',
        chapter: 100,
        status: SeriesStatus.READING,
      };

      const createdSeries = {
        id: 'integrity-test-id',
        ...testSeries,
        updated_at: new Date('2024-01-01'),
      };

      // Create series
      mockDatabaseService.seriesExists.mockResolvedValue(false);
      mockDatabaseService.createSeries.mockResolvedValue(createdSeries);

      const createResponse = await app.request('/api/series', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify(testSeries),
      });

      // Verify all fields are preserved
      const createBody = await createResponse.json();
      expect(createBody.data.name).toBe(testSeries.name);
      expect(createBody.data.chapter).toBe(testSeries.chapter);
      expect(createBody.data.status).toBe(testSeries.status);
      expect(createBody.data.id).toBeDefined();
      expect(createBody.data.updated_at).toBeDefined();

      // Update and verify changes
      const updatedSeries = {
        ...createdSeries,
        chapter: 150,
        updated_at: new Date('2024-01-02'),
      };

      mockDatabaseService.getSeriesById.mockResolvedValue(createdSeries);
      mockDatabaseService.seriesExists.mockResolvedValue(false);
      mockDatabaseService.updateSeries.mockResolvedValue(updatedSeries);

      const updateResponse = await app.request(`/api/series/${createdSeries.id}`, {
        method: 'PUT',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ chapter: 150 }),
      });

      const updateBody = await updateResponse.json();
      expect(updateBody.data.chapter).toBe(150);
      expect(updateBody.data.name).toBe(testSeries.name); // Should remain unchanged
      expect(updateBody.data.status).toBe(testSeries.status); // Should remain unchanged
    });
  });

  describe('API Contract Tests', () => {
    it('should maintain consistent response structure', async () => {
      mockDatabaseService.getAllSeries.mockResolvedValue({
        data: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
      });

      const response = await app.request('/api/series');

      expect(response.status).toBe(200);
      const responseBody = await response.json();

      // Verify response structure
      expect(responseBody).toHaveProperty('success');
      expect(responseBody).toHaveProperty('data');
      expect(responseBody.data).toHaveProperty('data');
      expect(responseBody.data).toHaveProperty('pagination');

      // Verify pagination structure
      const pagination = responseBody.data.pagination;
      expect(pagination).toHaveProperty('page');
      expect(pagination).toHaveProperty('limit');
      expect(pagination).toHaveProperty('total');
      expect(pagination).toHaveProperty('totalPages');
      expect(pagination).toHaveProperty('hasNext');
      expect(pagination).toHaveProperty('hasPrev');
    });

    it('should return proper HTTP status codes', async () => {
      // Test 200 OK
      mockDatabaseService.getAllSeries.mockResolvedValue({
        data: [],
        pagination: { page: 1, limit: 10, total: 0, totalPages: 0, hasNext: false, hasPrev: false },
      });
      const okResponse = await app.request('/api/series');
      expect(okResponse.status).toBe(200);

      // Test 201 Created
      mockDatabaseService.seriesExists.mockResolvedValue(false);
      mockDatabaseService.createSeries.mockResolvedValue({
        id: 'test-id',
        name: 'Test',
        chapter: 1,
        status: SeriesStatus.READING,
        updated_at: new Date(),
      });
      const createdResponse = await app.request('/api/series', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: 'Test' }),
      });
      expect(createdResponse.status).toBe(201);

      // Test 400 Bad Request
      const badResponse = await app.request('/api/series', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: '' }),
      });
      expect(badResponse.status).toBe(400);

      // Test 404 Not Found
      mockDatabaseService.getSeriesById.mockResolvedValue(null);
      const notFoundResponse = await app.request('/api/series/non-existent-id');
      expect(notFoundResponse.status).toBe(404);

      // Test 409 Conflict
      mockDatabaseService.seriesExists.mockResolvedValue(true);
      const conflictResponse = await app.request('/api/series', {
        method: 'POST',
        headers: { 'Content-Type': 'application/json' },
        body: JSON.stringify({ name: 'Existing' }),
      });
      expect(conflictResponse.status).toBe(409);
    });
  });
});
