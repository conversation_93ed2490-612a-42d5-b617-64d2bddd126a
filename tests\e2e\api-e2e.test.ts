import request from 'supertest';
import app from '../../src/index';
import { SeriesStatus } from '../../src/types/series';

// Mock the database service for E2E tests
jest.mock('../../src/services/database');

describe('End-to-End API Tests', () => {
  let mockDatabaseService: any;
  let createdSeriesId: string;

  beforeEach(() => {
    jest.clearAllMocks();
    
    mockDatabaseService = {
      getAllSeries: jest.fn(),
      getSeriesById: jest.fn(),
      createSeries: jest.fn(),
      updateSeries: jest.fn(),
      deleteSeries: jest.fn(),
      seriesExists: jest.fn(),
    };

    const { DatabaseService } = require('../../src/services/database');
    DatabaseService.mockImplementation(() => mockDatabaseService);
  });

  describe('Complete CRUD Workflow', () => {
    it('should complete a full CRUD lifecycle', async () => {
      const testSeries = {
        name: 'E2E Test Series',
        chapter: 1,
        status: SeriesStatus.READING,
      };

      const createdSeries = {
        id: 'e2e-test-id-123',
        ...testSeries,
        updated_at: new Date('2024-01-01'),
      };

      const updatedSeries = {
        ...createdSeries,
        chapter: 50,
        status: SeriesStatus.COMPLETED,
        updated_at: new Date('2024-01-02'),
      };

      // Step 1: Create a new series
      mockDatabaseService.seriesExists.mockResolvedValue(false);
      mockDatabaseService.createSeries.mockResolvedValue(createdSeries);

      const createResponse = await request(app)
        .post('/api/series')
        .send(testSeries)
        .expect(201);

      expect(createResponse.body.success).toBe(true);
      expect(createResponse.body.data.name).toBe(testSeries.name);
      createdSeriesId = createResponse.body.data.id;

      // Step 2: Retrieve the created series
      mockDatabaseService.getSeriesById.mockResolvedValue(createdSeries);

      const getResponse = await request(app)
        .get(`/api/series/${createdSeriesId}`)
        .expect(200);

      expect(getResponse.body.success).toBe(true);
      expect(getResponse.body.data.id).toBe(createdSeriesId);

      // Step 3: Update the series
      mockDatabaseService.getSeriesById.mockResolvedValue(createdSeries);
      mockDatabaseService.seriesExists.mockResolvedValue(false);
      mockDatabaseService.updateSeries.mockResolvedValue(updatedSeries);

      const updateResponse = await request(app)
        .put(`/api/series/${createdSeriesId}`)
        .send({
          chapter: 50,
          status: SeriesStatus.COMPLETED,
        })
        .expect(200);

      expect(updateResponse.body.success).toBe(true);
      expect(updateResponse.body.data.chapter).toBe(50);
      expect(updateResponse.body.data.status).toBe(SeriesStatus.COMPLETED);

      // Step 4: Verify the series appears in the list
      mockDatabaseService.getAllSeries.mockResolvedValue({
        data: [updatedSeries],
        pagination: {
          page: 1,
          limit: 10,
          total: 1,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
      });

      const listResponse = await request(app)
        .get('/api/series')
        .expect(200);

      expect(listResponse.body.success).toBe(true);
      expect(listResponse.body.data.data).toHaveLength(1);
      expect(listResponse.body.data.data[0].id).toBe(createdSeriesId);

      // Step 5: Delete the series
      mockDatabaseService.getSeriesById.mockResolvedValue(updatedSeries);
      mockDatabaseService.deleteSeries.mockResolvedValue(true);

      const deleteResponse = await request(app)
        .delete(`/api/series/${createdSeriesId}`)
        .expect(200);

      expect(deleteResponse.body.success).toBe(true);
      expect(deleteResponse.body.message).toBe('Series deleted successfully');

      // Step 6: Verify the series is gone
      mockDatabaseService.getSeriesById.mockResolvedValue(null);

      await request(app)
        .get(`/api/series/${createdSeriesId}`)
        .expect(404);
    });
  });

  describe('Complex Query Scenarios', () => {
    it('should handle complex filtering and pagination', async () => {
      const mockSeries = Array.from({ length: 25 }, (_, i) => ({
        id: `series-${i}`,
        name: `Test Series ${i}`,
        chapter: i * 10,
        status: i % 3 === 0 ? SeriesStatus.READING : 
               i % 3 === 1 ? SeriesStatus.COMPLETED : SeriesStatus.ON_HOLD,
        updated_at: new Date(`2024-01-${String(i + 1).padStart(2, '0')}`),
      }));

      // Test pagination
      mockDatabaseService.getAllSeries.mockResolvedValue({
        data: mockSeries.slice(0, 10),
        pagination: {
          page: 1,
          limit: 10,
          total: 25,
          totalPages: 3,
          hasNext: true,
          hasPrev: false,
        },
      });

      const paginationResponse = await request(app)
        .get('/api/series?page=1&limit=10')
        .expect(200);

      expect(paginationResponse.body.data.data).toHaveLength(10);
      expect(paginationResponse.body.data.pagination.hasNext).toBe(true);

      // Test filtering by status
      const readingSeries = mockSeries.filter(s => s.status === SeriesStatus.READING);
      mockDatabaseService.getAllSeries.mockResolvedValue({
        data: readingSeries,
        pagination: {
          page: 1,
          limit: 10,
          total: readingSeries.length,
          totalPages: Math.ceil(readingSeries.length / 10),
          hasNext: false,
          hasPrev: false,
        },
      });

      const filterResponse = await request(app)
        .get(`/api/series?status=${SeriesStatus.READING}`)
        .expect(200);

      expect(filterResponse.body.data.data.every((s: any) => s.status === SeriesStatus.READING)).toBe(true);

      // Test search functionality
      const searchResults = mockSeries.filter(s => s.name.includes('5'));
      mockDatabaseService.getAllSeries.mockResolvedValue({
        data: searchResults,
        pagination: {
          page: 1,
          limit: 10,
          total: searchResults.length,
          totalPages: 1,
          hasNext: false,
          hasPrev: false,
        },
      });

      const searchResponse = await request(app)
        .get('/api/series?search=5')
        .expect(200);

      expect(searchResponse.body.data.data.every((s: any) => s.name.includes('5'))).toBe(true);
    });
  });

  describe('Error Handling Scenarios', () => {
    it('should handle database connection errors gracefully', async () => {
      mockDatabaseService.getAllSeries.mockRejectedValue(new Error('Database connection failed'));

      const response = await request(app)
        .get('/api/series')
        .expect(500);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Internal server error');
    });

    it('should handle validation errors consistently', async () => {
      const invalidSeries = {
        name: '', // Invalid empty name
        chapter: -5, // Invalid negative chapter
        status: 'InvalidStatus', // Invalid status
      };

      const response = await request(app)
        .post('/api/series')
        .send(invalidSeries)
        .expect(400);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Invalid request data');
      expect(response.body.details).toBeDefined();
    });

    it('should handle duplicate name conflicts', async () => {
      const duplicateSeries = {
        name: 'Existing Series',
        chapter: 1,
        status: SeriesStatus.READING,
      };

      mockDatabaseService.seriesExists.mockResolvedValue(true);

      const response = await request(app)
        .post('/api/series')
        .send(duplicateSeries)
        .expect(409);

      expect(response.body.success).toBe(false);
      expect(response.body.error).toBe('Series with this name already exists');
    });
  });

  describe('Performance Under Load', () => {
    it('should handle multiple concurrent requests', async () => {
      mockDatabaseService.getAllSeries.mockResolvedValue({
        data: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
      });

      const concurrentRequests = Array.from({ length: 20 }, () =>
        request(app).get('/api/series').expect(200)
      );

      const startTime = Date.now();
      const responses = await Promise.all(concurrentRequests);
      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(1000); // Should complete within 1 second
      expect(responses.every(res => res.body.success)).toBe(true);
    });

    it('should maintain response times under load', async () => {
      const largeSeries = {
        name: 'A'.repeat(255), // Maximum length name
        chapter: 999999,
        status: SeriesStatus.PLAN_TO_READ,
      };

      mockDatabaseService.seriesExists.mockResolvedValue(false);
      mockDatabaseService.createSeries.mockResolvedValue({
        id: 'large-series-id',
        ...largeSeries,
        updated_at: new Date(),
      });

      const startTime = Date.now();
      
      const response = await request(app)
        .post('/api/series')
        .send(largeSeries)
        .expect(201);

      const endTime = Date.now();

      expect(endTime - startTime).toBeLessThan(100); // Should respond quickly
      expect(response.body.success).toBe(true);
    });
  });

  describe('Data Integrity Tests', () => {
    it('should maintain data consistency across operations', async () => {
      const testSeries = {
        name: 'Data Integrity Test',
        chapter: 100,
        status: SeriesStatus.READING,
      };

      const createdSeries = {
        id: 'integrity-test-id',
        ...testSeries,
        updated_at: new Date('2024-01-01'),
      };

      // Create series
      mockDatabaseService.seriesExists.mockResolvedValue(false);
      mockDatabaseService.createSeries.mockResolvedValue(createdSeries);

      const createResponse = await request(app)
        .post('/api/series')
        .send(testSeries);

      // Verify all fields are preserved
      expect(createResponse.body.data.name).toBe(testSeries.name);
      expect(createResponse.body.data.chapter).toBe(testSeries.chapter);
      expect(createResponse.body.data.status).toBe(testSeries.status);
      expect(createResponse.body.data.id).toBeDefined();
      expect(createResponse.body.data.updated_at).toBeDefined();

      // Update and verify changes
      const updatedSeries = {
        ...createdSeries,
        chapter: 150,
        updated_at: new Date('2024-01-02'),
      };

      mockDatabaseService.getSeriesById.mockResolvedValue(createdSeries);
      mockDatabaseService.seriesExists.mockResolvedValue(false);
      mockDatabaseService.updateSeries.mockResolvedValue(updatedSeries);

      const updateResponse = await request(app)
        .put(`/api/series/${createdSeries.id}`)
        .send({ chapter: 150 });

      expect(updateResponse.body.data.chapter).toBe(150);
      expect(updateResponse.body.data.name).toBe(testSeries.name); // Should remain unchanged
      expect(updateResponse.body.data.status).toBe(testSeries.status); // Should remain unchanged
    });
  });

  describe('API Contract Tests', () => {
    it('should maintain consistent response structure', async () => {
      mockDatabaseService.getAllSeries.mockResolvedValue({
        data: [],
        pagination: {
          page: 1,
          limit: 10,
          total: 0,
          totalPages: 0,
          hasNext: false,
          hasPrev: false,
        },
      });

      const response = await request(app)
        .get('/api/series')
        .expect(200);

      // Verify response structure
      expect(response.body).toHaveProperty('success');
      expect(response.body).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('data');
      expect(response.body.data).toHaveProperty('pagination');
      
      // Verify pagination structure
      const pagination = response.body.data.pagination;
      expect(pagination).toHaveProperty('page');
      expect(pagination).toHaveProperty('limit');
      expect(pagination).toHaveProperty('total');
      expect(pagination).toHaveProperty('totalPages');
      expect(pagination).toHaveProperty('hasNext');
      expect(pagination).toHaveProperty('hasPrev');
    });

    it('should return proper HTTP status codes', async () => {
      // Test 200 OK
      mockDatabaseService.getAllSeries.mockResolvedValue({
        data: [],
        pagination: { page: 1, limit: 10, total: 0, totalPages: 0, hasNext: false, hasPrev: false },
      });
      await request(app).get('/api/series').expect(200);

      // Test 201 Created
      mockDatabaseService.seriesExists.mockResolvedValue(false);
      mockDatabaseService.createSeries.mockResolvedValue({
        id: 'test-id',
        name: 'Test',
        chapter: 1,
        status: SeriesStatus.READING,
        updated_at: new Date(),
      });
      await request(app).post('/api/series').send({ name: 'Test' }).expect(201);

      // Test 400 Bad Request
      await request(app).post('/api/series').send({ name: '' }).expect(400);

      // Test 404 Not Found
      mockDatabaseService.getSeriesById.mockResolvedValue(null);
      await request(app).get('/api/series/non-existent-id').expect(404);

      // Test 409 Conflict
      mockDatabaseService.seriesExists.mockResolvedValue(true);
      await request(app).post('/api/series').send({ name: 'Existing' }).expect(409);
    });
  });
});
